#pragma once

#define _WINERROR_

#define NO_ERROR				0
#define ERROR_INVALID_FUNCTION			1
#define ERROR_NOT_ENOUGH_MEMORY			8
#define ERROR_DEV_NOT_EXIST			55
#define ERROR_INVALID_PARAMETER			87
#define ERROR_INSUFFICIENT_BUFFER		122
#define ERROR_INVALID_NAME			123
#define ERROR_BUSY				170
#define ERROR_MORE_DATA				234
#define WAIT_TIMEOUT				258
#define ERROR_IO_PENDING			997
#define ERROR_DEVICE_REINITIALIZATION_NEEDED	1164
#define ERROR_CONTINUE				1246
#define ERROR_NO_MORE_DEVICES			1248

