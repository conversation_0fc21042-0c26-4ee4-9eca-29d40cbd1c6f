/*!
 *
 * BOOTLICKER
 *
 * GuidePoint Security LLC
 *
 * Threat and Attack Simulation Team
 *
!*/

#pragma once

#include <windows.h>
#include <intrin.h>
#include "global/Labels.h"
#include "global/Macros.h"
#include "global/Config.h"
#include "global/Hash.h"
#include "global/Pe.h"
#include "gnu-efi/efi.h"

#include "OslArchTransferToKernel.h"
#include "ExitBootServices.h"
#include "DrvMain.h"
#include "EfiMain.h"
#include "Native.h"
#include "EfTbl.h"
