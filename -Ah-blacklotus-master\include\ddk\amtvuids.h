
#ifndef OUR_GUID_ENTRY
#define OUR_GUID_ENTRY(name, l, w1, w2, b1, b2, b3, b4, b5, b6, b7, b8) \
DEFINE_GUID(name, l, w1, w2, b1, b2, b3, b4, b5, b6, b7, b8);
#endif

OUR_GUID_ENTRY(CLSID_CTVTunerFilter,             0x266EEE40, 0x6C63, 0x11CF, 0x8A, 0x3, 0x0, 0xAA, 0x0, 0x6E, 0xCB, 0x65);
OUR_GUID_ENTRY(CLSID_CTVTunerFilterPropertyPage, 0x266EEE41, 0x6C63, 0x11CF, 0x8A, 0x3, 0x0, 0xAA, 0x0, 0x6E, 0xCB, 0x65);
OUR_GUID_ENTRY(IID_AnalogVideoStandard,          0x266EEE44, 0x6C63, 0x11CF, 0x8A, 0x3, 0x0, 0xAA, 0x0, 0x6E, 0xCB, 0x65);
OUR_GUID_ENTRY(IID_TunerInputType,               0x266EEE46, 0x6C63, 0x11CF, 0x8A, 0x3, 0x0, 0xAA, 0x0, 0x6E, 0xCB, 0x65);
OUR_GUID_ENTRY(CLSID_CrossbarFilter,             0x71F96460, 0x78F3, 0x11D0, 0xA1, 0x8C, 0x0, 0xA0, 0xC9, 0x11, 0x89, 0x56);
OUR_GUID_ENTRY(CLSID_CrossbarFilterPropertyPage, 0x71F96461, 0x78F3, 0x11D0, 0xA1, 0x8C, 0x0, 0xA0, 0xC9, 0x11, 0x89, 0x56);
OUR_GUID_ENTRY(CLSID_TVAudioFilter,              0x71F96462, 0x78F3, 0x11D0, 0xA1, 0x8C, 0x0, 0xA0, 0xC9, 0x11, 0x89, 0x56);
OUR_GUID_ENTRY(CLSID_TVAudioFilterPropertyPage,  0x71F96463, 0x78F3, 0x11D0, 0xA1, 0x8C, 0x0, 0xA0, 0xC9, 0x11, 0x89, 0x56);

