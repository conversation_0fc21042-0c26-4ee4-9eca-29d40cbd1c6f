#ifndef _ADAPTER_DEBUG_H
#define _ADAPTER_DEBUG_H

/*++

Copyright (c) 1999  Intel Corporation

Module Name:

    AdapterDebug.h
    
Abstract:

    Protocol to debug the EDD 3.0 enablement of BIOS option ROMs



Revision History

--*/

// {82F86881-282B-11d4-BC7D-0080C73C8881}
#define ADAPTER_DEBUG_PROTOCOL \
{ 0x82f86881, 0x282b, 0x11d4, {0xbc, 0x7d, 0x0, 0x80, 0xc7, 0x3c, 0x88, 0x81} }

//
// This protocol points to the BIOS_LEGACY_DRIVE data structure
//  see edd.h for more details
//

#endif

