
#
# This is a machine generated file - DO NOT EDIT
#    Generated by genmake.exe
#    Generated from make.inf
#    Copyright (c) 1998  Intel Corporation
#

INC_DEPS = $(INC_DEPS) \
    $(SDK_INSTALL_DIR)\include\efi\protocol\efivar.h \
    $(SDK_INSTALL_DIR)\include\efi\protocol\legacyboot.h \
    $(SDK_INSTALL_DIR)\include\efi\protocol\vgaclass.h \
    $(SDK_INSTALL_DIR)\include\efi\protocol\efidbg.h \


!IF "$(PROCESSOR)" == "Ia32"
INC_DEPS = $(INC_DEPS) \


!ENDIF


!IF "$(PROCESSOR)" == "Ia64"
INC_DEPS = $(INC_DEPS) \
    $(SDK_INSTALL_DIR)\include\efi\protocol\$(PROCESSOR)\eficontext.h \


!ENDIF

