
#
# This is a machine generated file - DO NOT EDIT
#    Generated by gen<PERSON>ke.exe
#    Generated from make.inf
#    Copyright (c) 1998  Intel Corporation
#

INC_DEPS = $(INC_DEPS) \
    $(SDK_INSTALL_DIR)\include\efi\efi.h \
    $(SDK_INSTALL_DIR)\include\efi\efiapi.h \
    $(SDK_INSTALL_DIR)\include\efi\eficon.h \
    $(SDK_INSTALL_DIR)\include\efi\efidebug.h \
    $(SDK_INSTALL_DIR)\include\efi\efidef.h \
    $(SDK_INSTALL_DIR)\include\efi\efidevp.h \
    $(SDK_INSTALL_DIR)\include\efi\efierr.h \
    $(SDK_INSTALL_DIR)\include\efi\efifs.h \
    $(SDK_INSTALL_DIR)\include\efi\efilib.h \
    $(SDK_INSTALL_DIR)\include\efi\efipart.h \
    $(SDK_INSTALL_DIR)\include\efi\efipciio.h \
    $(SDK_INSTALL_DIR)\include\efi\efiprot.h \
    $(SDK_INSTALL_DIR)\include\efi\efipxebc.h \
    $(SDK_INSTALL_DIR)\include\efi\efistdarg.h \
    $(SDK_INSTALL_DIR)\include\efi\efinet.h \
    $(SDK_INSTALL_DIR)\include\efi\efiip.h \
    $(SDK_INSTALL_DIR)\include\efi\efiudp.h \
    $(SDK_INSTALL_DIR)\include\efi\efitcp.h \


!IF "$(PROCESSOR)" == "Ia32"
INC_DEPS = $(INC_DEPS) \
    $(SDK_INSTALL_DIR)\include\efi\Ia32\efibind.h \
    $(SDK_INSTALL_DIR)\include\efi\Ia32\pe.h \
    $(SDK_INSTALL_DIR)\include\efi\Ia32\efilibplat.h \


!ENDIF


!IF "$(PROCESSOR)" == "Ia64"
INC_DEPS = $(INC_DEPS) \
    $(SDK_INSTALL_DIR)\include\efi\Ia64\efibind.h \
    $(SDK_INSTALL_DIR)\include\efi\Ia64\pe.h \
    $(SDK_INSTALL_DIR)\include\efi\Ia64\efilibplat.h \


!ENDIF

